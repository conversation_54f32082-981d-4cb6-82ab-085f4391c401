﻿using LibBaseModules.Json;
using LibBusinessModules.Config.FaultManagement.Models;
using System.Collections.Generic;
using System.Linq;

namespace LibBusinessModules.Config
{
    /// <summary>
    /// 故障码配置管理类
    /// </summary>
    public class FaultManager : BaseJsonNode
    {
        #region 字段属性

        /// <summary>
        /// 存储从JSON文件加载的故障配置数据。
        /// </summary>
        public FaultInfo FaultInfoList { get; set; } = new FaultInfo();

        #endregion

        #region 单例

        /// <summary>
        /// 用于确保单例模式线程安全的锁对象。
        /// </summary>
        private static readonly object SyncObj = new object();
        /// <summary>
        /// FaultManager的静态单例实例。
        /// </summary>
        private static FaultManager _instance = null;

        /// <summary>
        /// 获取 FaultManager 的单例实例。
        /// 使用双重检查锁定（Double-Check Locking）确保线程安全。
        /// </summary>
        /// <returns>FaultManager的单例实例。</returns>
        public static FaultManager GetInstance()
        {
            lock(SyncObj)
            {
                if(_instance == null)
                {
                    _instance = new FaultManager();
                }
            }
            return _instance;
        }

        /// <summary>
        /// 私有构造函数，用于防止外部直接实例化。
        /// 在构造时会调用 loadJson() 方法加载配置数据。
        /// </summary>
        private FaultManager()
        {
            loadJson();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 重置单例实例。
        /// 这将导致在下一次调用 GetInstance() 时，强制从JSON文件中重新加载数据。
        /// </summary>
        public void ReLoad()
        {
            _instance = null;
        }

        /// <summary>
        /// 保存故障配置数据到JSON文件
        /// </summary>
        public void SaveConfig()
        {
            this.Save();
        }

        #region 故障一级分类操作

        /// <summary>
        /// 获取所有一级分类
        /// </summary>
        public List<FaultCategory1> GetAllCategory1()
        {
            return FaultInfoList.Category1List.OrderBy(x => x.Id).ToList();
        }

        /// <summary>
        /// 添加一级分类
        /// 新添加的分类会自动初始化其 SubCategories 列表。
        /// <param name="category">要添加的一级分类对象。</param>
        /// <returns>添加的分类的ID。</returns>
        /// </summary>
        public int AddCategory1(FaultCategory1 category)
        {
            category.SubCategories = new List<FaultCategory2>();

            FaultInfoList.Category1List.Add(category);

            return category.Id;
        }

        /// <summary>
        /// 更新一级分类
        /// <param name="category">包含更新信息的一级分类对象。</param>
        /// <returns>如果找到并成功更新分类，则返回 true；否则返回 false。</returns>
        /// </summary>
        public bool UpdateCategory1(FaultCategory1 category)
        {
            var existing = FaultInfoList.Category1List.FirstOrDefault(c => c.Id == category.Id);
            if(existing != null)
            {
                existing.CategoryName = category.CategoryName;
                existing.Description = category.Description;

                return true;
            }
            return false;
        }

        /// <summary>
        /// 删除一级分类
        /// <param name="id">要删除的一级分类的ID。</param>
        /// </summary>
        public void DeleteCategory1(int id)
        {
            FaultInfoList.Category1List.RemoveAll(c => c.Id == id);
        }

        /// <summary>
        /// 根据ID获取一级分类
        /// <param name="id">要获取的一级分类的ID。</param>
        /// <returns>找到的一级分类对象；如果未找到，则返回 null。</returns>
        /// </summary>
        public FaultCategory1 GetCategory1ById(int id)
        {
            return FaultInfoList.Category1List.FirstOrDefault(c => c.Id == id);
        }

        #endregion

        #region 故障二级分类操作

        /// <summary>
        /// 根据一级分类ID获取二级分类
        /// <param name="category1Id">父一级分类的ID。</param>
        /// <returns>一个包含二级分类的列表，按ID排序；如果父分类不存在或没有子分类，则返回空列表。</returns>
        /// </summary>
        public List<FaultCategory2> GetCategory2ByParentId(int category1Id)
        {
            var category1 = FaultInfoList.Category1List.FirstOrDefault(c => c.Id == category1Id);
            return category1?.SubCategories.OrderBy(x => x.Id).ToList() ?? new List<FaultCategory2>();
        }

        /// <summary>
        /// 添加二级分类
        /// <param name="category">要添加的二级分类对象，其 Category1Id 必须已设置。</param>
        /// <returns>添加的二级分类的ID；如果其父一级分类未找到，则返回 0。</returns>
        /// </summary>
        public int AddCategory2(FaultCategory2 category)
        {
            var parentCategory = FaultInfoList.Category1List.FirstOrDefault(c => c.Id == category.Category1Id);
            if(parentCategory != null)
            {
                parentCategory.SubCategories.Add(category);

                return category.Id;
            }
            return 0;
        }

        /// <summary>
        /// 更新二级分类
        /// <param name="category">包含更新信息的二级分类对象。</param>
        /// <returns>如果找到并成功更新分类，则返回 true；否则返回 false。</returns>
        /// </summary>
        public bool UpdateCategory2(FaultCategory2 category)
        {
            var parentCategory = FaultInfoList.Category1List.FirstOrDefault(c => c.Id == category.Category1Id);
            var existing = parentCategory?.SubCategories.FirstOrDefault(c => c.Id == category.Id);

            if(existing != null)
            {
                existing.CategoryName = category.CategoryName;
                existing.Description = category.Description;

                return true;
            }
            return false;
        }

        /// <summary>
        /// 删除二级分类
        /// <param name="id">要删除的二级分类的ID。</param>
        /// <returns>如果找到并成功删除分类，则返回 true；否则返回 false。</returns>
        /// </summary>
        public bool DeleteCategory2(int id)
        {
            foreach(var category1 in FaultInfoList.Category1List)
            {
                var category2 = category1.SubCategories.FirstOrDefault(c => c.Id == id);
                if(category2 != null)
                {
                    category1.SubCategories.Remove(category2);
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 根据ID获取二级分类
        /// <param name="id">要获取的二级分类的ID。</param>
        /// <returns>找到的二级分类对象；如果未找到，则返回 null。</returns>
        /// </summary>
        public FaultCategory2 GetCategory2ById(int id)
        {
            foreach(var category1 in FaultInfoList.Category1List)
            {
                var category2 = category1.SubCategories.FirstOrDefault(c => c.Id == id);
                if(category2 != null)
                    return category2;
            }
            return null;
        }

        #endregion

        #endregion
    }
}
