# UC_FaultCategoryManager 故障分类管理界面功能说明

## 📋 概述
UC_FaultCategoryManager 已重构为树形结构的故障类别管理界面，提供直观的层级显示和便捷的编辑功能。

## 🎯 新增功能特性

### 1. 自动生成节点编号和默认名称
- **新增一级分类**：自动生成ID（1, 2, 3...），默认名称为"Default"
- **新增二级分类**：自动生成ID（1, 2, 3...），默认名称为"Default"
- **自动选中**：新增节点后自动选中并聚焦到名称输入框

### 2. 左右联动编辑
- **实时同步**：右侧编辑区域的名称修改会实时反映到左侧树形结构
- **即时更新**：无需保存即可看到树节点名称的变化
- **数据同步**：同时更新界面显示和底层数据模型

### 3. 树形结构显示
- **根节点**：固定为"故障类别"
- **一级节点**：主类别
- **二级节点**：子类别
- **层级展示**：清晰的父子关系显示

### 4. 右键上下文菜单
- **根节点**：仅支持"新增"（新增一级分类）
- **一级节点**：支持"新增"、"编辑"、"删除"
- **二级节点**：支持"编辑"、"删除"

## 🚀 使用方法

### 新增分类
1. **新增一级分类**：
   - 在根节点"故障类别"上右键
   - 选择"新增"
   - 系统自动创建名为"Default"的新节点
   - 自动选中新节点并聚焦到名称输入框
   - 修改名称和描述信息

2. **新增二级分类**：
   - 在一级分类节点上右键
   - 选择"新增"
   - 系统自动创建名为"Default"的子节点
   - 自动选中新节点并聚焦到名称输入框
   - 修改名称和描述信息

### 编辑分类
1. **选择节点**：点击树形结构中的节点
2. **查看详情**：右侧面板显示详细信息
3. **实时编辑**：
   - 修改故障名称：输入框中的变化立即反映到树节点
   - 修改故障描述：在多行文本框中编辑
   - 查看故障编号：只读显示，自动生成
   - 查看所属分类：二级节点显示父分类名称

### 删除分类
1. **选择节点**：右键点击要删除的节点
2. **选择删除**：从上下文菜单选择"删除"
3. **确认操作**：系统提示确认删除
4. **依赖检查**：
   - 一级分类：检查是否有子分类，有则不允许删除
   - 二级分类：直接删除

### 保存和恢复
- **保存**：点击底部"保存"按钮保存所有修改
- **恢复**：点击底部"恢复"按钮撤销所有未保存的修改

## 🔧 技术实现

### 核心方法
- `AddLevel1Category()` - 新增一级分类
- `AddLevel2Category()` - 新增二级分类
- `txtCategoryName_TextChanged()` - 文本变化事件处理
- `FindNodeByTag()` - 根据数据对象查找树节点
- `LoadNodeDetails()` - 加载节点详细信息

### 数据流程
1. **新增流程**：创建数据对象 → 添加到管理器 → 刷新树形结构 → 查找并选中新节点 → 聚焦编辑
2. **编辑流程**：文本变化 → 更新树节点显示 → 更新数据模型
3. **保存流程**：验证数据 → 调用管理器保存 → 刷新界面

### 事件处理
- 使用临时移除事件处理机制避免循环更新
- 在加载数据时暂停TextChanged事件
- 完成加载后重新启用事件监听

## 📝 注意事项
1. **编号生成**：基于现有数据的最大ID+1
2. **名称同步**：修改名称时立即更新树节点和数据模型
3. **事件管理**：避免循环触发TextChanged事件
4. **数据验证**：保存时检查名称不能为空
5. **依赖检查**：删除一级分类时检查子分类存在性

## 🎨 界面特点
- **响应式布局**：左侧树形控件350px，右侧详情区域自适应
- **用户友好**：操作提示清晰，错误信息明确
- **视觉一致**：使用SunnyUI控件库保持界面风格统一
- **交互流畅**：实时反馈，即时更新
