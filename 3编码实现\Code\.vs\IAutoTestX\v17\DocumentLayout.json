{"Version": 1, "WorkspaceRootPath": "G:\\01-My<PERSON><PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|g:\\01-mycode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\code\\business\\libbusinessmodules\\config\\faultmanagement\\faultmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|solutionrelative:business\\libbusinessmodules\\config\\faultmanagement\\faultmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|g:\\01-mycode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\code\\business\\libbusinessmodules\\config\\faultmanagement\\ui\\frmfaultcategoryedit.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|solutionrelative:business\\libbusinessmodules\\config\\faultmanagement\\ui\\frmfaultcategoryedit.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|g:\\01-mycode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\code\\business\\libbusinessmodules\\config\\faultmanagement\\model\\models.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|solutionrelative:business\\libbusinessmodules\\config\\faultmanagement\\model\\models.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|g:\\01-mycode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\code\\business\\libbusinessmodules\\config\\faultmanagement\\ui\\uc_faultcategorymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|solutionrelative:business\\libbusinessmodules\\config\\faultmanagement\\ui\\uc_faultcategorymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|g:\\01-mycode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\code\\business\\libbusinessmodules\\config\\faultmanagement\\ui\\uc_faultcategorymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|solutionrelative:business\\libbusinessmodules\\config\\faultmanagement\\ui\\uc_faultcategorymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|g:\\01-mycode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\code\\business\\libbusinessmodules\\config\\faultmanagement\\ui\\frmfaultcategoryedit.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|solutionrelative:business\\libbusinessmodules\\config\\faultmanagement\\ui\\frmfaultcategoryedit.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|g:\\01-mycode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\code\\business\\libbusinessmodules\\ui\\systemconfig\\frmsystemconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|solutionrelative:business\\libbusinessmodules\\ui\\systemconfig\\frmsystemconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|g:\\01-mycode\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\code\\business\\libbusinessmodules\\ui\\systemconfig\\frmsystemconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}|Business\\LibBusinessModules\\LibBusinessModules.csproj|solutionrelative:business\\libbusinessmodules\\ui\\systemconfig\\frmsystemconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 13, "Children": [{"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:134:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:135:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:137:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:136:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Models.cs", "DocumentMoniker": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\Config\\FaultManagement\\Model\\Models.cs", "RelativeDocumentMoniker": "Business\\LibBusinessModules\\Config\\FaultManagement\\Model\\Models.cs", "ToolTip": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\Config\\FaultManagement\\Model\\Models.cs", "RelativeToolTip": "Business\\LibBusinessModules\\Config\\FaultManagement\\Model\\Models.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAABdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T05:55:04.846Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "FrmFaultCategoryEdit.cs", "DocumentMoniker": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\FrmFaultCategoryEdit.cs", "RelativeDocumentMoniker": "Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\FrmFaultCategoryEdit.cs", "ToolTip": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\FrmFaultCategoryEdit.cs", "RelativeToolTip": "Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\FrmFaultCategoryEdit.cs", "ViewState": "AgIAAJkAAAAAAAAAAAAYwEQAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T05:41:06.434Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "FrmFaultCategoryEdit.cs [设计]", "DocumentMoniker": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\FrmFaultCategoryEdit.cs", "RelativeDocumentMoniker": "Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\FrmFaultCategoryEdit.cs", "ToolTip": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\FrmFaultCategoryEdit.cs [设计]", "RelativeToolTip": "Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\FrmFaultCategoryEdit.cs [设计]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T05:41:23.396Z", "EditorCaption": " [设计]"}, {"$type": "Document", "DocumentIndex": 0, "Title": "FaultManager.cs", "DocumentMoniker": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\Config\\FaultManagement\\FaultManager.cs", "RelativeDocumentMoniker": "Business\\LibBusinessModules\\Config\\FaultManagement\\FaultManager.cs", "ToolTip": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\Config\\FaultManagement\\FaultManager.cs", "RelativeToolTip": "Business\\LibBusinessModules\\Config\\FaultManagement\\FaultManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T03:43:26.457Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "FrmSystemConfig.cs", "DocumentMoniker": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\UI\\SystemConfig\\FrmSystemConfig.cs", "RelativeDocumentMoniker": "Business\\LibBusinessModules\\UI\\SystemConfig\\FrmSystemConfig.cs", "ToolTip": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\UI\\SystemConfig\\FrmSystemConfig.cs", "RelativeToolTip": "Business\\LibBusinessModules\\UI\\SystemConfig\\FrmSystemConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T03:42:36.882Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "UC_FaultCategoryManager.cs", "DocumentMoniker": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\UC_FaultCategoryManager.cs", "RelativeDocumentMoniker": "Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\UC_FaultCategoryManager.cs", "ToolTip": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\UC_FaultCategoryManager.cs", "RelativeToolTip": "Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\UC_FaultCategoryManager.cs", "ViewState": "AgIAAEsAAAAAAAAAAAAjwGAAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T03:40:47.259Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "UC_FaultCategoryManager.cs [设计]", "DocumentMoniker": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\UC_FaultCategoryManager.cs", "RelativeDocumentMoniker": "Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\UC_FaultCategoryManager.cs", "ToolTip": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\UC_FaultCategoryManager.cs [设计]", "RelativeToolTip": "Business\\LibBusinessModules\\Config\\FaultManagement\\UI\\UC_FaultCategoryManager.cs [设计]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T03:40:45.843Z", "EditorCaption": " [设计]"}, {"$type": "Document", "DocumentIndex": 7, "Title": "FrmSystemConfig.cs [设计]", "DocumentMoniker": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\UI\\SystemConfig\\FrmSystemConfig.cs", "RelativeDocumentMoniker": "Business\\LibBusinessModules\\UI\\SystemConfig\\FrmSystemConfig.cs", "ToolTip": "G:\\01-MyC<PERSON>\\00-mywork\\02-faang\\04-自动化测试软件\\3编码实现\\Code\\Business\\LibBusinessModules\\UI\\SystemConfig\\FrmSystemConfig.cs [设计]", "RelativeToolTip": "Business\\LibBusinessModules\\UI\\SystemConfig\\FrmSystemConfig.cs [设计]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T03:40:27.943Z", "EditorCaption": " [设计]"}]}]}]}