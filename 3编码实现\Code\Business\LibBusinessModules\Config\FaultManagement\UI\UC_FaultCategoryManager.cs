using LibBusinessModules.Config.FaultManagement.Models;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace LibBusinessModules.Config.FaultManagement.UI
{
    /// <summary>
    /// 故障分类管理界面 - 树形结构
    /// </summary>
    public partial class UC_FaultCategoryManager : UIUserControl
    {
        #region 字段

        /// <summary>
        /// 当前选中的节点
        /// </summary>
        private TreeNode _selectedNode;

        /// <summary>
        /// 当前编辑模式
        /// </summary>
        private EditMode _currentEditMode = EditMode.None;

        /// <summary>
        /// 根节点名称
        /// </summary>
        private const string ROOT_NODE_TEXT = "故障类别";

        #endregion

        #region 构造

        public UC_FaultCategoryManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_FaultCategoryManager_Load(object sender, EventArgs e)
        {
            if (!DesignMode)
            {
                InitializeTreeView();
                RefreshTreeView();
                ClearDetailPanel();
            }
        }

        /// <summary>
        /// 树形控件节点选择事件
        /// </summary>
        private void treeViewCategory_AfterSelect(object sender, TreeViewEventArgs e)
        {
            _selectedNode = e.Node;
            LoadNodeDetails(e.Node);
        }

        /// <summary>
        /// 右键菜单 - 新增
        /// </summary>
        private void menuItemAdd_Click(object sender, EventArgs e)
        {
            AddCategory();
        }

        /// <summary>
        /// 右键菜单 - 编辑
        /// </summary>
        private void menuItemEdit_Click(object sender, EventArgs e)
        {
            EditCategory();
        }

        /// <summary>
        /// 右键菜单 - 删除
        /// </summary>
        private void menuItemDelete_Click(object sender, EventArgs e)
        {
            DeleteCategory();
        }



        /// <summary>
        /// 保存修改
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            if (SaveCurrentEdit())
            {
                FaultManager.GetInstance().SaveConfig();
                UIMessageBox.ShowSuccess("保存成功！");
            }
        }

        /// <summary>
        /// 重置修改
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            if (UIMessageBox.ShowAsk("确定要恢复所有修改吗？"))
            {
                FaultManager.GetInstance().ReLoad();
                RefreshTreeView();
                ClearDetailPanel();
                UIMessageBox.ShowSuccess("重置修改成功！");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化树形控件
        /// </summary>
        private void InitializeTreeView()
        {
            treeViewCategory.Nodes.Clear();

            // 创建根节点
            TreeNode rootNode = new TreeNode(ROOT_NODE_TEXT)
            {
                Tag = null, // 根节点没有关联数据
                ImageIndex = 0,
                SelectedImageIndex = 0
            };

            treeViewCategory.Nodes.Add(rootNode);
            rootNode.Expand();
        }

        /// <summary>
        /// 刷新树形结构
        /// </summary>
        private void RefreshTreeView()
        {
            if (treeViewCategory.Nodes.Count == 0)
            {
                InitializeTreeView();
            }

            TreeNode rootNode = treeViewCategory.Nodes[0];
            rootNode.Nodes.Clear();

            // 加载一级分类
            List<FaultCategory1> categories = FaultManager.GetInstance().GetAllCategory1();
            foreach (var category1 in categories)
            {
                TreeNode category1Node = new TreeNode(category1.CategoryName)
                {
                    Tag = category1,
                    ImageIndex = 1,
                    SelectedImageIndex = 1
                };

                // 加载二级分类
                foreach (var category2 in category1.SubCategories)
                {
                    TreeNode category2Node = new TreeNode(category2.CategoryName)
                    {
                        Tag = category2,
                        ImageIndex = 2,
                        SelectedImageIndex = 2
                    };
                    category1Node.Nodes.Add(category2Node);
                }

                rootNode.Nodes.Add(category1Node);
                category1Node.Expand();
            }

            rootNode.Expand();
        }

        /// <summary>
        /// 加载节点详细信息到右侧面板
        /// </summary>
        private void LoadNodeDetails(TreeNode node)
        {
            if (node == null)
            {
                ClearDetailPanel();
                return;
            }

            if (node.Tag is FaultCategory1 category1)
            {
                // 一级分类
                txtCategoryId.Text = category1.Id.ToString();
                txtCategoryName.Text = category1.CategoryName;
                txtDescription.Text = category1.Description;
                txtParentCategory.Text = "";
                txtParentCategory.Visible = false;
                uiLabel4.Visible = false;

                _currentEditMode = EditMode.EditLevel1;
            }
            else if (node.Tag is FaultCategory2 category2)
            {
                // 二级分类
                txtCategoryId.Text = category2.Id.ToString();
                txtCategoryName.Text = category2.CategoryName;
                txtDescription.Text = category2.Description;

                // 显示所属一级分类
                var parentCategory = FaultManager.GetInstance().GetCategory1ById(category2.Category1Id);
                txtParentCategory.Text = parentCategory?.CategoryName ?? "";
                txtParentCategory.Visible = true;
                uiLabel4.Visible = true;

                _currentEditMode = EditMode.EditLevel2;
            }
            else
            {
                // 根节点
                ClearDetailPanel();
                _currentEditMode = EditMode.None;
            }
        }

        /// <summary>
        /// 清空详细信息面板
        /// </summary>
        private void ClearDetailPanel()
        {
            txtCategoryId.Text = "";
            txtCategoryName.Text = "";
            txtDescription.Text = "";
            txtParentCategory.Text = "";
            txtParentCategory.Visible = false;
            uiLabel4.Visible = false;
            _currentEditMode = EditMode.None;
        }

        /// <summary>
        /// 新增分类
        /// </summary>
        private void AddCategory()
        {
            if (_selectedNode == null) return;

            if (_selectedNode.Tag == null) // 根节点，新增一级分类
            {
                AddLevel1Category();
            }
            else if (_selectedNode.Tag is FaultCategory1) // 一级分类节点，新增二级分类
            {
                AddLevel2Category((FaultCategory1)_selectedNode.Tag);
            }
            // 二级分类节点不能新增子节点
        }

        /// <summary>
        /// 编辑分类
        /// </summary>
        private void EditCategory()
        {
            if (_selectedNode == null || _selectedNode.Tag == null) return;

            if (_selectedNode.Tag is FaultCategory1 category1)
            {
                EditLevel1Category(category1);
            }
            else if (_selectedNode.Tag is FaultCategory2 category2)
            {
                EditLevel2Category(category2);
            }
        }

        /// <summary>
        /// 删除分类
        /// </summary>
        private void DeleteCategory()
        {
            if (_selectedNode == null || _selectedNode.Tag == null) return;

            if (_selectedNode.Tag is FaultCategory1 category1)
            {
                DeleteLevel1Category(category1);
            }
            else if (_selectedNode.Tag is FaultCategory2 category2)
            {
                DeleteLevel2Category(category2);
            }
        }

        /// <summary>
        /// 新增一级分类
        /// </summary>
        private void AddLevel1Category()
        {
            using var frm = new FrmFaultCategoryEdit(FaultCategoryLevel.Level1);
            if (frm.ShowDialog() == DialogResult.OK)
            {
                RefreshTreeView();
                UIMessageBox.ShowInfo("添加一级分类成功");
            }
        }

        /// <summary>
        /// 新增二级分类
        /// </summary>
        private void AddLevel2Category(FaultCategory1 parentCategory)
        {
            using var frm = new FrmFaultCategoryEdit(FaultCategoryLevel.Level2, null, parentCategory.Id);
            if (frm.ShowDialog() == DialogResult.OK)
            {
                RefreshTreeView();
                UIMessageBox.ShowInfo("添加二级分类成功");
            }
        }

        /// <summary>
        /// 编辑一级分类
        /// </summary>
        private void EditLevel1Category(FaultCategory1 category)
        {
            using var frm = new FrmFaultCategoryEdit(FaultCategoryLevel.Level1, category);
            if (frm.ShowDialog() == DialogResult.OK)
            {
                RefreshTreeView();
                LoadNodeDetails(_selectedNode); // 刷新详细信息
                UIMessageBox.ShowInfo("编辑一级分类成功");
            }
        }

        /// <summary>
        /// 编辑二级分类
        /// </summary>
        private void EditLevel2Category(FaultCategory2 category)
        {
            using var frm = new FrmFaultCategoryEdit(FaultCategoryLevel.Level2, category, category.Category1Id);
            if (frm.ShowDialog() == DialogResult.OK)
            {
                RefreshTreeView();
                LoadNodeDetails(_selectedNode); // 刷新详细信息
                UIMessageBox.ShowInfo("编辑二级分类成功");
            }
        }

        /// <summary>
        /// 删除一级分类
        /// </summary>
        private void DeleteLevel1Category(FaultCategory1 category)
        {
            if (category.SubCategories.Count > 0)
            {
                UIMessageBox.ShowError("该分类下存在二级分类，无法删除");
                return;
            }

            if (UIMessageBox.ShowAsk($"确定要删除分类 [{category.CategoryName}] 吗？"))
            {
                FaultManager.GetInstance().DeleteCategory1(category.Id);
                RefreshTreeView();
                ClearDetailPanel();
                UIMessageBox.ShowInfo("删除一级分类成功");
            }
        }

        /// <summary>
        /// 删除二级分类
        /// </summary>
        private void DeleteLevel2Category(FaultCategory2 category)
        {
            if (UIMessageBox.ShowAsk($"确定要删除分类 [{category.CategoryName}] 吗？"))
            {
                if (FaultManager.GetInstance().DeleteCategory2(category.Id))
                {
                    RefreshTreeView();
                    ClearDetailPanel();
                    UIMessageBox.ShowInfo("删除二级分类成功");
                }
            }
        }

        /// <summary>
        /// 保存当前编辑
        /// </summary>
        private bool SaveCurrentEdit()
        {
            if (_currentEditMode == EditMode.None || _selectedNode?.Tag == null)
                return true;

            try
            {
                if (string.IsNullOrWhiteSpace(txtCategoryName.Text))
                {
                    UIMessageBox.ShowError("故障名称不能为空");
                    return false;
                }

                if (_currentEditMode == EditMode.EditLevel1 && _selectedNode.Tag is FaultCategory1 category1)
                {
                    category1.CategoryName = txtCategoryName.Text.Trim();
                    category1.Description = txtDescription.Text.Trim();
                    FaultManager.GetInstance().UpdateCategory1(category1);
                }
                else if (_currentEditMode == EditMode.EditLevel2 && _selectedNode.Tag is FaultCategory2 category2)
                {
                    category2.CategoryName = txtCategoryName.Text.Trim();
                    category2.Description = txtDescription.Text.Trim();
                    FaultManager.GetInstance().UpdateCategory2(category2);
                }

                RefreshTreeView();
                return true;
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError($"保存失败：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成故障编号
        /// </summary>
        private string GenerateFaultId(FaultCategoryLevel level, int? parentId = null)
        {
            if (level == FaultCategoryLevel.Level1)
            {
                var categories = FaultManager.GetInstance().GetAllCategory1();
                int maxId = categories.Count > 0 ? categories.Max(c => c.Id) : 0;
                return (maxId + 1).ToString("D3");
            }
            else
            {
                if (parentId.HasValue)
                {
                    var categories = FaultManager.GetInstance().GetCategory2ByParentId(parentId.Value);
                    int maxId = categories.Count > 0 ? categories.Max(c => c.Id) : 0;
                    return $"{parentId:D3}-{(maxId + 1):D3}";
                }
            }
            return "";
        }

        #endregion
    }

    /// <summary>
    /// 编辑模式枚举
    /// </summary>
    public enum EditMode
    {
        /// <summary>
        /// 无编辑
        /// </summary>
        None,

        /// <summary>
        /// 编辑一级分类
        /// </summary>
        EditLevel1,

        /// <summary>
        /// 编辑二级分类
        /// </summary>
        EditLevel2
    }

    /// <summary>
    /// 故障分类级别枚举
    /// </summary>
    public enum FaultCategoryLevel
    {
        /// <summary>
        /// 一级分类
        /// </summary>
        Level1 = 1,

        /// <summary>
        /// 二级分类
        /// </summary>
        Level2 = 2
    }
}