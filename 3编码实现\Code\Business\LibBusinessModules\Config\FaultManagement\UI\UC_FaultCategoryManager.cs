using LibBusinessModules.Config.FaultManagement.Models;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Windows.Forms;

namespace LibBusinessModules.Config.FaultManagement.UI
{
    /// <summary>
    /// 故障分类管理界面
    /// </summary>
    public partial class UC_FaultCategoryManager : UIUserControl
    {
        #region 构造

        public UC_FaultCategoryManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_FaultCategoryManager_Load(object sender, EventArgs e)
        {
            RefreshCategory1List();
        }

        private void dgvCategory1_SelectionChanged(object sender, EventArgs e)
        {
            if(dgvCategory1.SelectedRows.Count > 0)
            {
                int category1Id = Convert.ToInt32(dgvCategory1.SelectedRows[0].Cells["Id"].Value);
                RefreshCategory2List(category1Id);
            }
            else
            {
                dgvCategory2.Rows.Clear();
            }
        }

        private void btnAddCategory1_Click(object sender, EventArgs e)
        {
            using(var frm = new FrmFaultCategoryEdit(FaultCategoryLevel.Level1))
            {
                if(frm.ShowDialog() == DialogResult.OK)
                {
                    RefreshCategory1List();
                    UIMessageBox.ShowInfo("添加一级分类成功");
                }
            }
        }

        private void btnEditCategory1_Click(object sender, EventArgs e)
        {
            if(dgvCategory1.SelectedRows.Count == 0)
            {
                UIMessageBox.ShowError("请先选择要编辑的一级分类");
                return;
            }

            int category1Id = Convert.ToInt32(dgvCategory1.SelectedRows[0].Cells["Id"].Value);
            var category = FaultManager.GetInstance().GetCategory1ById(category1Id);

            using(var frm = new FrmFaultCategoryEdit(FaultCategoryLevel.Level1, category))
            {
                if(frm.ShowDialog() == DialogResult.OK)
                {
                    RefreshCategory1List();
                    UIMessageBox.ShowInfo("编辑一级分类成功");
                }
            }
        }

        private void btnDeleteCategory1_Click(object sender, EventArgs e)
        {
            if(dgvCategory1.SelectedRows.Count == 0)
            {
                UIMessageBox.ShowError("请先选择要删除的一级分类");
                return;
            }

            int category1Id = Convert.ToInt32(dgvCategory1.SelectedRows[0].Cells["Id"].Value);
            var category = FaultManager.GetInstance().GetCategory1ById(category1Id);

            if(category.SubCategories.Count > 0)
            {
                UIMessageBox.ShowError("该分类下存在二级分类，无法删除");
                return;
            }

            if(UIMessageBox.ShowAsk($"确定要删除分类 [{category.CategoryName}] 吗？"))
            {
                FaultManager.GetInstance().DeleteCategory1(category1Id);
                RefreshCategory1List();
                UIMessageBox.ShowInfo("删除一级分类成功");
            }
        }

        private void btnAddCategory2_Click(object sender, EventArgs e)
        {
            if(dgvCategory1.SelectedRows.Count == 0)
            {
                UIMessageBox.ShowError("请先选择一级分类");
                return;
            }

            int category1Id = Convert.ToInt32(dgvCategory1.SelectedRows[0].Cells["Id"].Value);

            using(var frm = new FrmFaultCategoryEdit(FaultCategoryLevel.Level2, null, category1Id))
            {
                if(frm.ShowDialog() == DialogResult.OK)
                {
                    RefreshCategory2List(category1Id);
                    UIMessageBox.ShowInfo("添加二级分类成功");
                }
            }
        }

        private void btnEditCategory2_Click(object sender, EventArgs e)
        {
            if(dgvCategory2.SelectedRows.Count == 0)
            {
                UIMessageBox.ShowError("请先选择要编辑的二级分类");
                return;
            }

            int category2Id = Convert.ToInt32(dgvCategory2.SelectedRows[0].Cells["Id"].Value);
            int category1Id = Convert.ToInt32(dgvCategory2.SelectedRows[0].Cells["Category1Id"].Value);

            var category1 = FaultManager.GetInstance().GetCategory1ById(category1Id);
            var category2 = category1.SubCategories.Find(c => c.Id == category2Id);

            using(var frm = new FrmFaultCategoryEdit(FaultCategoryLevel.Level2, category2, category1Id))
            {
                if(frm.ShowDialog() == DialogResult.OK)
                {
                    RefreshCategory2List(category1Id);
                    UIMessageBox.ShowInfo("编辑二级分类成功");
                }
            }
        }

        private void btnDeleteCategory2_Click(object sender, EventArgs e)
        {
            if(dgvCategory2.SelectedRows.Count == 0)
            {
                UIMessageBox.ShowError("请先选择要删除的二级分类");
                return;
            }

            int category2Id = Convert.ToInt32(dgvCategory2.SelectedRows[0].Cells["Id"].Value);
            string categoryName = dgvCategory2.SelectedRows[0].Cells["CategoryName"].Value.ToString();

            if(UIMessageBox.ShowAsk($"确定要删除分类 [{categoryName}] 吗？"))
            {
                // 注意：DeleteCategory2方法目前只是检查是否存在，需要修改实现真正的删除功能
                if(FaultManager.GetInstance().DeleteCategory2(category2Id))
                {
                    // 这里需要实现真正的删除逻辑
                    int category1Id = Convert.ToInt32(dgvCategory1.SelectedRows[0].Cells["Id"].Value);
                    RefreshCategory2List(category1Id);
                    UIMessageBox.ShowInfo("删除二级分类成功");
                }
            }
        }

        /// <summary>
        /// 保存修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            FaultManager.GetInstance().SaveConfig();
            UIMessageBox.ShowSuccess("保存成功！");
        }

        /// <summary>
        /// 重置修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            FaultManager.GetInstance().ReLoad();
            RefreshCategory1List();
            UIMessageBox.ShowSuccess("重置修改成功！");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新一级分类列表
        /// </summary>
        private void RefreshCategory1List()
        {
            dgvCategory1.Rows.Clear();
            List<FaultCategory1> categories = FaultManager.GetInstance().GetAllCategory1();

            foreach(var category in categories)
            {
                int rowIndex = dgvCategory1.AddRow();
                DataGridViewRow row = dgvCategory1.Rows[rowIndex];
                row.Cells["Id"].Value = category.Id;
                row.Cells["CategoryName"].Value = category.CategoryName;
                row.Cells["Description"].Value = category.Description;
                row.Cells["SubCount"].Value = category.SubCategories.Count;
            }

            if(dgvCategory1.Rows.Count > 0)
            {
                dgvCategory1.SelectedRows[0].Selected = true;
            }
        }

        /// <summary>
        /// 刷新二级分类列表
        /// </summary>
        /// <param name="category1Id">一级分类ID</param>
        private void RefreshCategory2List(int category1Id)
        {
            dgvCategory2.Rows.Clear();
            List<FaultCategory2> categories = FaultManager.GetInstance().GetCategory2ByParentId(category1Id);

            foreach(var category in categories)
            {
                int rowIndex = dgvCategory2.AddRow();
                DataGridViewRow row = dgvCategory2.Rows[rowIndex];
                row.Cells["Id"].Value = category.Id;
                row.Cells["Category1Id"].Value = category.Category1Id;
                row.Cells["CategoryName"].Value = category.CategoryName;
                row.Cells["Description"].Value = category.Description;
            }
        }

        #endregion
    }

    /// <summary>
    /// 故障分类级别枚举
    /// </summary>
    public enum FaultCategoryLevel
    {
        /// <summary>
        /// 一级分类
        /// </summary>
        Level1 = 1,

        /// <summary>
        /// 二级分类
        /// </summary>
        Level2 = 2
    }
}