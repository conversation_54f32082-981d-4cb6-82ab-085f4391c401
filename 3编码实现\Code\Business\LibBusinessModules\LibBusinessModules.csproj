<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C31FDB8D-27DB-4088-B49D-8A27AF53CF63}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LibBusinessModules</RootNamespace>
    <AssemblyName>LibBusinessModules</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\..\Product\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\..\Product\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Config\DBInfo\UI\UC\UC_DBInfoManager.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Config\DBInfo\UI\UC\UC_DBInfoManager.Designer.cs">
      <DependentUpon>UC_DBInfoManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Config\EmployeeInfo\EmployeeInfo.cs" />
    <Compile Include="Config\DBInfo\DBInfo.cs" />
    <Compile Include="Config\FaultManagement\FaultManager.cs" />
    <Compile Include="Config\FaultManagement\Model\Models.cs" />
    <Compile Include="Config\FaultManagement\UI\FrmFaultCategoryEdit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Config\FaultManagement\UI\FrmFaultCategoryEdit.Designer.cs">
      <DependentUpon>FrmFaultCategoryEdit.cs</DependentUpon>
    </Compile>
    <Compile Include="Config\FaultManagement\UI\UC_FaultCategoryManager.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Config\FaultManagement\UI\UC_FaultCategoryManager.Designer.cs">
      <DependentUpon>UC_FaultCategoryManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Config\FTPInfo\FTPInfo.cs" />
    <Compile Include="Config\FTPInfo\UI\UC\UC_FTPInfoManager.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Config\FTPInfo\UI\UC\UC_FTPInfoManager.Designer.cs">
      <DependentUpon>UC_FTPInfoManager.cs</DependentUpon>
    </Compile>
    <Compile Include="DB\Fault\Model\FaultCategory.cs" />
    <Compile Include="DB\Model\Device\RawAddCheckData.cs" />
    <Compile Include="DB\Model\Device\RawAlarmData.cs" />
    <Compile Include="DB\Model\Device\RawDoubleCheckData.cs" />
    <Compile Include="DB\Model\Device\RawOperData.cs" />
    <Compile Include="DB\Model\Device\RawSpanCheckData.cs" />
    <Compile Include="DB\Model\Device\RawZeroCheckData.cs" />
    <Compile Include="DB\Model\PC\OperData.cs" />
    <Compile Include="DB\Model\PC\AlarmData.cs" />
    <Compile Include="Config\Device\DeviceConfig.cs" />
    <Compile Include="Config\Device\DeviceManager.cs" />
    <Compile Include="DB\Helper\DBHelper.cs" />
    <Compile Include="DB\Model\Device\RawAdjData.cs" />
    <Compile Include="DB\Model\Device\AdjSignalInfo.cs" />
    <Compile Include="DB\Model\Device\DeviceSNInfo.cs" />
    <Compile Include="DB\Model\Device\FlowVersionInfo.cs" />
    <Compile Include="DB\Model\Device\HmiVersionInfo.cs" />
    <Compile Include="DB\Model\Device\ImnRawAdjdata.cs" />
    <Compile Include="DB\Model\Device\ImnRawMeasuredata.cs" />
    <Compile Include="DB\Model\Device\LineData.cs" />
    <Compile Include="DB\Model\Device\MainVersionInfo.cs" />
    <Compile Include="DB\Model\Device\RawMeasureData.cs" />
    <Compile Include="DB\Model\Device\PumpSNInfo.cs" />
    <Compile Include="DB\Model\Device\PumpVersionInfo.cs" />
    <Compile Include="DB\Model\Device\TnRawAdjdata.cs" />
    <Compile Include="DB\Model\Device\TnRawMeasuredata.cs" />
    <Compile Include="DB\Model\Device\ValveSNInfo.cs" />
    <Compile Include="DB\Model\Device\ValveVersionInfo.cs" />
    <Compile Include="DB\Model\PC\LightSourceInfo.cs" />
    <Compile Include="DB\Model\PC\DeviceInfo.cs" />
    <Compile Include="DB\Model\PC\CalibrationData.cs" />
    <Compile Include="DB\Model\PC\ImnCalibrationData.cs" />
    <Compile Include="DB\Model\PC\ImnMeasureData.cs" />
    <Compile Include="DB\Model\PC\CurveData.cs" />
    <Compile Include="DB\Model\PC\MeasureData.cs" />
    <Compile Include="DB\Model\PC\AddCheckData.cs" />
    <Compile Include="DB\Model\PC\DoubleCheckData.cs" />
    <Compile Include="DB\Model\PC\SpanCheckData.cs" />
    <Compile Include="DB\Model\PC\ZeroCheckData.cs" />
    <Compile Include="DB\Model\PC\TnCalibrationData.cs" />
    <Compile Include="DB\Model\PC\TnMeasureData.cs" />
    <Compile Include="DB\Model\OneDeviceAllInfo.cs" />
    <Compile Include="Config\SystemConfig.cs" />
    <Compile Include="Config\Device\UI\FrmDeviceConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Config\Device\UI\FrmDeviceConfig.Designer.cs">
      <DependentUpon>FrmDeviceConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="Config\Device\UI\UC\UC_MeasureItemConfig.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Config\Device\UI\UC\UC_MeasureItemConfig.Designer.cs">
      <DependentUpon>UC_MeasureItemConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="Config\Device\UI\UC\UC_DeviceManager.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Config\Device\UI\UC\UC_DeviceManager.Designer.cs">
      <DependentUpon>UC_DeviceManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Helper\AppInfoHelper.cs" />
    <Compile Include="Helper\GlobalHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Config\EmployeeInfo\UI\FrmEmployeeInfoConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Config\EmployeeInfo\UI\FrmEmployeeInfoConfig.Designer.cs">
      <DependentUpon>FrmEmployeeInfoConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="Config\EmployeeInfo\UI\UC\UC_EmployeeInfoManager.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Config\EmployeeInfo\UI\UC\UC_EmployeeInfoManager.Designer.cs">
      <DependentUpon>UC_EmployeeInfoManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\Config\DeviceRawReportData.cs" />
    <Compile Include="Report\Config\ReportCalculateNode.cs" />
    <Compile Include="Report\Config\ReportCalibrationData.cs" />
    <Compile Include="Report\Config\ReportCurveData.cs" />
    <Compile Include="Report\Config\ReportMeasureData.cs" />
    <Compile Include="Report\Helper\DataRecordExportHelper.cs" />
    <Compile Include="Report\Helper\InspectionRecordExportHelper.cs" />
    <Compile Include="Report\Helper\CertificateExportHelper.cs" />
    <Compile Include="Report\Helper\ReportDocumentHelper.cs" />
    <Compile Include="Report\Helper\ReportPathHelper.cs" />
    <Compile Include="Report\UI\UC\UC_ReportDataShow.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Report\UI\UC\UC_ReportDataShow.Designer.cs">
      <DependentUpon>UC_ReportDataShow.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\UI\UC\UC_DeviceSelect.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Report\UI\UC\UC_DeviceSelect.Designer.cs">
      <DependentUpon>UC_DeviceSelect.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_AlarmQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_AlarmQuery.Designer.cs">
      <DependentUpon>UC_AlarmQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_CalibrationDataQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_CalibrationDataQuery.Designer.cs">
      <DependentUpon>UC_CalibrationDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_DoubleCheckDataQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_DoubleCheckDataQuery.Designer.cs">
      <DependentUpon>UC_DoubleCheckDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_ZeroCheckDataQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_ZeroCheckDataQuery.Designer.cs">
      <DependentUpon>UC_ZeroCheckDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_SpanCheckDataQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_SpanCheckDataQuery.Designer.cs">
      <DependentUpon>UC_SpanCheckDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_AddCheckDataQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_AddCheckDataQuery.Designer.cs">
      <DependentUpon>UC_AddCheckDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_LightSourceInfoQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_LightSourceInfoQuery.designer.cs">
      <DependentUpon>UC_LightSourceInfoQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_DeviceInfoQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_DeviceInfoQuery.designer.cs">
      <DependentUpon>UC_DeviceInfoQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_ImnCalibrationDataQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_ImnCalibrationDataQuery.Designer.cs">
      <DependentUpon>UC_ImnCalibrationDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_ImnMeasureDataQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_ImnMeasureDataQuery.Designer.cs">
      <DependentUpon>UC_ImnMeasureDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_TnCalibrationDataQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_TnCalibrationDataQuery.Designer.cs">
      <DependentUpon>UC_TnCalibrationDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_TnMeasureDataQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_TnMeasureDataQuery.Designer.cs">
      <DependentUpon>UC_TnMeasureDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_MeasureDataQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_MeasureDataQuery.Designer.cs">
      <DependentUpon>UC_MeasureDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_CurveDataQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_CurveDataQuery.Designer.cs">
      <DependentUpon>UC_CurveDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Commom\FrmAboutMe.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Commom\FrmAboutMe.Designer.cs">
      <DependentUpon>FrmAboutMe.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\User\FrmChangePwd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\User\FrmChangePwd.Designer.cs">
      <DependentUpon>FrmChangePwd.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\SystemConfig\FrmSystemConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\SystemConfig\FrmSystemConfig.Designer.cs">
      <DependentUpon>FrmSystemConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\FrmMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\FrmMain.Designer.cs">
      <DependentUpon>FrmMain.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\User\FrmUserLogin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\User\FrmUserLogin.Designer.cs">
      <DependentUpon>FrmUserLogin.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Commom\FrmWelcome.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Commom\FrmWelcome.Designer.cs">
      <DependentUpon>FrmWelcome.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\UC_DataQueryBase.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\UC_DataQueryBase.designer.cs">
      <DependentUpon>UC_DataQueryBase.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_LogQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Sub\UC_LogQuery.Designer.cs">
      <DependentUpon>UC_LogQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\UC_AllDataQuery.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\UC_AllDataQuery.Designer.cs">
      <DependentUpon>UC_AllDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Fault\UC_FaultManager.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Fault\UC_FaultManager.Designer.cs">
      <DependentUpon>UC_FaultManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\UI\UC\UC_ReportExport.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Report\UI\UC\UC_ReportExport.Designer.cs">
      <DependentUpon>UC_ReportExport.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Com\UC_ComTest.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Com\UC_ComTest.Designer.cs">
      <DependentUpon>UC_ComTest.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataExtract\UC_DataExtract.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataExtract\UC_DataExtract.Designer.cs">
      <DependentUpon>UC_DataExtract.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Report\UC_ReportCenter.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\Report\UC_ReportCenter.Designer.cs">
      <DependentUpon>UC_ReportCenter.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Config\DBInfo\UI\UC\UC_DBInfoManager.resx">
      <DependentUpon>UC_DBInfoManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\Device\UI\FrmDeviceConfig.resx">
      <DependentUpon>FrmDeviceConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\Device\UI\UC\UC_MeasureItemConfig.resx">
      <DependentUpon>UC_MeasureItemConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\Device\UI\UC\UC_DeviceManager.resx">
      <DependentUpon>UC_DeviceManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\FaultManagement\UI\FrmFaultCategoryEdit.resx">
      <DependentUpon>FrmFaultCategoryEdit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\FaultManagement\UI\UC_FaultCategoryManager.resx">
      <DependentUpon>UC_FaultCategoryManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\FTPInfo\UI\UC\UC_FTPInfoManager.resx">
      <DependentUpon>UC_FTPInfoManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\EmployeeInfo\UI\FrmEmployeeInfoConfig.resx">
      <DependentUpon>FrmEmployeeInfoConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Config\EmployeeInfo\UI\UC\UC_EmployeeInfoManager.resx">
      <DependentUpon>UC_EmployeeInfoManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\UI\UC\UC_ReportDataShow.resx">
      <DependentUpon>UC_ReportDataShow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\UI\UC\UC_DeviceSelect.resx">
      <DependentUpon>UC_DeviceSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_AlarmQuery.resx">
      <DependentUpon>UC_AlarmQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_CalibrationDataQuery.resx">
      <DependentUpon>UC_CalibrationDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_DoubleCheckDataQuery.resx">
      <DependentUpon>UC_DoubleCheckDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_ZeroCheckDataQuery.resx">
      <DependentUpon>UC_ZeroCheckDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_SpanCheckDataQuery.resx">
      <DependentUpon>UC_SpanCheckDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_AddCheckDataQuery.resx">
      <DependentUpon>UC_AddCheckDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_LightSourceInfoQuery.resx">
      <DependentUpon>UC_LightSourceInfoQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_DeviceInfoQuery.resx">
      <DependentUpon>UC_DeviceInfoQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_ImnCalibrationDataQuery.resx">
      <DependentUpon>UC_ImnCalibrationDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_ImnMeasureDataQuery.resx">
      <DependentUpon>UC_ImnMeasureDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_TnCalibrationDataQuery.resx">
      <DependentUpon>UC_TnCalibrationDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_TnMeasureDataQuery.resx">
      <DependentUpon>UC_TnMeasureDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_MeasureDataQuery.resx">
      <DependentUpon>UC_MeasureDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_CurveDataQuery.resx">
      <DependentUpon>UC_CurveDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Commom\FrmAboutMe.resx">
      <DependentUpon>FrmAboutMe.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\User\FrmChangePwd.resx">
      <DependentUpon>FrmChangePwd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\SystemConfig\FrmSystemConfig.resx">
      <DependentUpon>FrmSystemConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\FrmMain.resx">
      <DependentUpon>FrmMain.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\User\FrmUserLogin.resx">
      <DependentUpon>FrmUserLogin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Commom\FrmWelcome.resx">
      <DependentUpon>FrmWelcome.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\UC_DataQueryBase.resx">
      <DependentUpon>UC_DataQueryBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Sub\UC_LogQuery.resx">
      <DependentUpon>UC_LogQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\UC_AllDataQuery.resx">
      <DependentUpon>UC_AllDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Fault\UC_FaultManager.resx">
      <DependentUpon>UC_FaultManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\UI\UC\UC_ReportExport.resx">
      <DependentUpon>UC_ReportExport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Com\UC_ComTest.resx">
      <DependentUpon>UC_ComTest.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataExtract\UC_DataExtract.resx">
      <DependentUpon>UC_DataExtract.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Report\UC_ReportCenter.resx">
      <DependentUpon>UC_ReportCenter.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="MySql.Data">
      <Version>9.2.0</Version>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json">
      <Version>13.0.3</Version>
    </PackageReference>
    <PackageReference Include="SqlSugar">
      <Version>5.1.4.187</Version>
    </PackageReference>
    <PackageReference Include="SunnyUI">
      <Version>3.8.2</Version>
    </PackageReference>
    <PackageReference Include="System.Data.SQLite">
      <Version>1.0.119</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LibBaseModules\LibBaseModules.csproj">
      <Project>{a510d96b-71bb-42b8-b2df-bb3bd8d89a72}</Project>
      <Name>LibBaseModules</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>